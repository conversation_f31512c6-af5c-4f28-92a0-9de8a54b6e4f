import time, os, sys, gc
import aicube
import image
import nncase_runtime as nn
import ujson
import ulab.numpy as np
from libs.PipeLine import ScopedTiming
from libs.Utils import *
from media.sensor import *
from media.display import *
from media.media import *
from machine import Pin, PWM, FPIOA
from machine import UART
uart = UART(3, 115200)
# === 配置显示模式 ===
DISPLAY_MODE = "lcd"  # "lcd" 或 "hdmi"
if DISPLAY_MODE == "lcd":
    DISPLAY_WIDTH = ALIGN_UP(800, 16)
    DISPLAY_HEIGHT = 480
else:
    DISPLAY_WIDTH = ALIGN_UP(1920, 16)
    DISPLAY_HEIGHT = 1080

# AI处理图像尺寸
OUT_RGB888P_WIDTH = ALIGN_UP(640, 16)
OUT_RGB888P_HEIGH = 360

# === 舵机配置 ===
def setup_servos():
    """初始化舵机引脚和PWM"""
    fpioa = FPIOA()
    # X轴舵机 (水平)
    fpioa.set_function(42, FPIOA.PWM0)
    servo_x = PWM(0, 50, duty=0, enable=True)
    # Y轴舵机 (垂直)
    fpioa.set_function(43, FPIOA.PWM1)
    servo_y = PWM(1, 50, duty=0, enable=True)
    return servo_x, servo_y

def set_servo_angle(servo, angle):
    """设置舵机角度（-90到90度）"""
    angle = max(-90, min(90, angle))
    duty = ((angle + 90) / 180 * 10 + 2.5)  # 角度转占空比
    servo.duty(duty)

# === PID控制器 ===
class IncrementalPID:
    def __init__(self, kp, ki, kd, imax):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.imax = imax
        self.last_error = 0
        self.integrator = 0

    def compute(self, error, dt):
        if dt < 0.01:
            dt = 0.01
        p_term = self.kp * error
        self.integrator += self.ki * error * dt
        self.integrator = max(-self.imax, min(self.imax, self.integrator))
        derivative = (error - self.last_error) / dt
        self.last_error = error
        return p_term + self.integrator + self.kd * derivative

    def reset(self):
        self.integrator = 0
        self.last_error = 0

# === 舵机平滑控制 ===
def smooth_update(current, target, max_step):
    if abs(target - current) <= max_step:
        return target
    return current + max_step if target > current else current - max_step

def update_position_pid(servo_x, servo_y, position_x, position_y, target_x, target_y, pid_x, pid_y, fps):
    """使用PID更新舵机位置"""
    error_x = target_x - position_x
    error_y = target_y - position_y

    dt = 1 / fps if fps > 0 else 0.1
    if dt < 0.02:
        dt = 0.02

    output_x = pid_x.compute(error_x, dt)
    output_y = pid_y.compute(error_y, dt)

    max_step_x = 5.0  # X轴最大步进角度
    max_step_y = 5.0  # Y轴最大步进角度

    new_position_x = smooth_update(position_x, position_x + output_x, max_step_x)
    new_position_y = smooth_update(position_y, position_y + output_y, max_step_y)

    new_position_x = max(-180, min(180, new_position_x))
    new_position_y = max(-180, min(180, new_position_y))

    # 更新舵机角度（当变化足够大时）
    if abs(new_position_x - position_x) > 0.5 or abs(new_position_y - position_y) > 0.5:
        set_servo_angle(servo_x, new_position_x)
        set_servo_angle(servo_y, new_position_y)

    return new_position_x, new_position_y

# === 目标检测辅助函数 ===
def two_side_pad_param(input_size, output_size):
    """计算填充参数"""
    ratio_w = output_size[0] / input_size[0]
    ratio_h = output_size[1] / input_size[1]
    ratio = min(ratio_w, ratio_h)
    new_w = int(ratio * input_size[0])
    new_h = int(ratio * input_size[1])
    dw = (output_size[0] - new_w) / 2
    dh = (output_size[1] - new_h) / 2
    top = int(round(dh - 0.1))
    bottom = int(round(dh + 0.1))
    left = int(round(dw - 0.1))
    right = int(round(dw - 0.1))
    return top, bottom, left, right, ratio

def read_deploy_config(config_path):
    """读取模型部署配置"""
    with open(config_path, "r") as json_file:
        try:
            return ujson.load(json_file)
        except ValueError as e:
            print("JSON解析错误:", e)
    return {}

# === 主函数 ===
def main():
    # 配置路径
    root_path = "/sdcard/mp_deployment_source/"
    config_path = root_path + "deploy_config.json"

    # 读取部署配置
    deploy_conf = read_deploy_config(config_path)
    if not deploy_conf:
        print("错误：无法读取部署配置")
        return

    # 提取配置参数
    kmodel_name = deploy_conf["kmodel_path"]
    labels = deploy_conf["categories"]
    confidence_threshold = deploy_conf["confidence_threshold"]
    nms_threshold = deploy_conf["nms_threshold"]
    img_size = deploy_conf["img_size"]
    num_classes = deploy_conf["num_classes"]
    color_four = get_colors(num_classes)
    nms_option = deploy_conf["nms_option"]
    model_type = deploy_conf["model_type"]

    if model_type == "AnchorBaseDet":
        anchors = deploy_conf["anchors"][0] + deploy_conf["anchors"][1] + deploy_conf["anchors"][2]

    kmodel_frame_size = img_size
    frame_size = [OUT_RGB888P_WIDTH, OUT_RGB888P_HEIGH]
    strides = [8, 16, 32]

    # 计算填充参数
    top, bottom, left, right, ratio = two_side_pad_param(frame_size, kmodel_frame_size)

    # 初始化舵机
    servo_x, servo_y = setup_servos()
    x_pos = 0  # X轴初始角度
    y_pos = 0  # Y轴初始角度
    set_servo_angle(servo_x, x_pos)
    set_servo_angle(servo_y, y_pos)

    # 初始化PID控制器
    #pid_x = IncrementalPID(0.6, 0.008, 0.08, 8)  # X轴PID
    #pid_y = IncrementalPID(0.6, 0.008, 0.04, 8)  # Y轴PID
    pid_x = IncrementalPID(0.85, 0.01, 0.09, 10)  # X轴PID
    pid_y = IncrementalPID(0.85, 0.01, 0.06, 10)  # Y轴PID
    # 目标追踪状态
    tracking_class = 0  # 默认追踪类别0
    last_target_time = time.ticks_ms()
    target_lost_count = 0

    # 初始化KPU
    kpu = nn.kpu()
    kpu.load_kmodel(root_path + kmodel_name)

    # 初始化AI2D
    ai2d = nn.ai2d()
    ai2d.set_dtype(nn.ai2d_format.NCHW_FMT, nn.ai2d_format.NCHW_FMT, np.uint8, np.uint8)
    ai2d.set_pad_param(True, [0, 0, 0, 0, top, bottom, left, right], 0, [114, 114, 114])
    ai2d.set_resize_param(True, nn.interp_method.tf_bilinear, nn.interp_mode.half_pixel)
    ai2d_builder = ai2d.build(
        [1, 3, OUT_RGB888P_HEIGH, OUT_RGB888P_WIDTH],
        [1, 3, kmodel_frame_size[1], kmodel_frame_size[0]]
    )

    # 初始化摄像头
    sensor = Sensor()
    sensor.reset()
    sensor.set_hmirror(False)
    sensor.set_vflip(False)

    # 通道0: 显示通道 (YUV420)
    sensor.set_framesize(width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT)
    sensor.set_pixformat(PIXEL_FORMAT_YUV_SEMIPLANAR_420)

    # 通道2: AI处理通道 (RGB888)
    sensor.set_framesize(width=OUT_RGB888P_WIDTH, height=OUT_RGB888P_HEIGH, chn=CAM_CHN_ID_2)
    sensor.set_pixformat(PIXEL_FORMAT_RGB_888_PLANAR, chn=CAM_CHN_ID_2)

    # 绑定显示通道
    sensor_bind_info = sensor.bind_info(x=0, y=0, chn=CAM_CHN_ID_0)
    Display.bind_layer(**sensor_bind_info, layer=Display.LAYER_VIDEO1)

    # 初始化显示
    if DISPLAY_MODE == "lcd":
        Display.init(Display.ST7701, width=800, height=480, to_ide=True)
    else:
        Display.init(Display.LT9611, to_ide=True)

    # 创建OSD层
    osd_img = image.Image(DISPLAY_WIDTH, DISPLAY_HEIGHT, image.ARGB8888)

    # 初始化媒体系统
    MediaManager.init()
    sensor.run()

    # 初始化AI2D输入输出张量
    rgb888p_img = None
    ai2d_input_tensor = None
    data = np.ones((1, 3, kmodel_frame_size[1], kmodel_frame_size[0]), dtype=np.uint8)
    ai2d_output_tensor = nn.from_numpy(data)

    # 帧率计算
    clock = time.clock()

    print("云台目标追踪系统已启动")

    # 主循环
    while True:
        clock.tick()

        try:
            # 获取AI处理图像
            rgb888p_img = sensor.snapshot(chn=CAM_CHN_ID_2)

            if rgb888p_img.format() == image.RGBP888:
                # 预处理图像
                ai2d_input = rgb888p_img.to_numpy_ref()
                ai2d_input_tensor = nn.from_numpy(ai2d_input)
                ai2d_builder.run(ai2d_input_tensor, ai2d_output_tensor)

                # 运行目标检测模型
                kpu.set_input_tensor(0, ai2d_output_tensor)
                kpu.run()

                # 获取模型输出
                results = []
                for i in range(kpu.outputs_size()):
                    out_data = kpu.get_output_tensor(i)
                    result = out_data.to_numpy()
                    result = result.reshape((result.shape[0] * result.shape[1] * result.shape[2] * result.shape[3]))
                    del out_data
                    results.append(result)

                # 后处理检测结果
                det_boxes = aicube.anchorbasedet_post_process(
                    results[0],
                    results[1],
                    results[2],
                    kmodel_frame_size,
                    frame_size,
                    strides,
                    num_classes,
                    confidence_threshold,
                    nms_threshold,
                    anchors,
                    nms_option,
                )

                # 清空OSD层
                osd_img.clear()

                # 目标追踪逻辑
                target_x, target_y = 0, 0
                found = False
                largest_area = 0
                largest_box = None

                if det_boxes:
                    # 寻找最大目标（可根据需要修改为特定类别）
                    for box in det_boxes:
                        class_id, conf, x1, y1, x2, y2 = box
                        area = (x2 - x1) * (y2 - y1)
                        if area > largest_area and class_id == tracking_class:
                            largest_area = area
                            largest_box = box

                    if largest_box:
                        class_id, conf, x1, y1, x2, y2 = largest_box
                        cx = (x1 + x2) / 2  # 目标中心X坐标
                        cy = (y1 + y2) / 2  # 目标中心Y坐标

                        # 将像素坐标转换为舵机角度
                        # 注意：这里需要根据实际安装方向调整符号
                        target_x = -(cx - OUT_RGB888P_WIDTH/2) * 0.15
                        target_y = (cy - OUT_RGB888P_HEIGH/2) * 0.1333

                        # 限制角度范围
                        target_x = max(-90, min(90, target_x))
                        target_y = max(-90, min(90, target_y))

                        found = True
                        last_target_time = time.ticks_ms()
                        target_lost_count = 0

                        # 绘制目标框和中心点
                        x = int(x1 * DISPLAY_WIDTH // OUT_RGB888P_WIDTH)
                        y = int(y1 * DISPLAY_HEIGHT // OUT_RGB888P_HEIGH)
                        w = int((x2 - x1) * DISPLAY_WIDTH // OUT_RGB888P_WIDTH)
                        h = int((y2 - y1) * DISPLAY_HEIGHT // OUT_RGB888P_HEIGH)

                        osd_img.draw_rectangle(x, y, w, h, color=color_four[class_id][1:])
                        osd_img.draw_cross(x + w//2, y + h//2, color=color_four[class_id][1:], size=10)

                        text = f"{labels[class_id]} {conf:.2f}"
                        osd_img.draw_string_advanced(x, y - 25, 32, text, color=color_four[class_id][1:])

                # 更新舵机位置
                if found:
                    x_pos, y_pos = update_position_pid(
                        servo_x, servo_y, x_pos, y_pos, target_x, target_y, pid_x, pid_y, clock.fps()
                    )
                    # 检测到物体时发送数据
                    data = bytearray([0xa1,0xb1,0x11,0x22,0xc1])
                    uart.write(data)
                else:
                    # 目标丢失处理
                    target_lost_count += 1
                    # 未检测到物体时发送数据
                    data = bytearray([0xa1,0xb1,0x22,0x11,0xc1])
                    uart.write(data)

                    # 超过2秒没有目标则回中
                    if time.ticks_diff(time.ticks_ms(), last_target_time) > 2000:
                        if abs(x_pos) > 1 or abs(y_pos) > 1:
                            x_pos, y_pos = update_position_pid(
                                servo_x, servo_y, x_pos, y_pos, 0, 0, pid_x, pid_y, clock.fps()
                            )

                # 显示状态信息
                status_text = f"目标: {'锁定' if found else '丢失'}"
                osd_img.draw_string(10, 10, status_text, color=(255, 255, 255))

                angle_text = f"云台: X:{x_pos:.1f}° Y:{y_pos:.1f}°"
                osd_img.draw_string(10, 40, angle_text, color=(255, 255, 255))

                fps_text = f"FPS: {clock.fps():.1f}"
                osd_img.draw_string(DISPLAY_WIDTH - 150, 10, fps_text, color=(255, 255, 255))

                # 显示目标丢失计数器
                if not found and target_lost_count > 0:
                    lost_text = f"丢失计数: {target_lost_count}"
                    osd_img.draw_string(10, 70, lost_text, color=(255, 100, 100))

                # 更新显示
                Display.show_image(osd_img, 0, 0, Display.LAYER_OSD3)

            # 垃圾回收
            gc.collect()
            rgb888p_img = None

        except Exception as e:
            print(f"错误: {e}")
            time.sleep(0.1)

# === 程序入口 ===
if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        # 清理资源
        print("程序终止")
        Display.deinit()
        MediaManager.deinit()
        gc.collect()
        time.sleep(1)
        nn.shrink_memory_pool()
        sys.exit(0)
